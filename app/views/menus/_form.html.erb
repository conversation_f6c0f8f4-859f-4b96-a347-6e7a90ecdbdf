<div class="card bg-base-100 shadow-xl">
  <div class="card-body">
    <%= form_with(model: [:dashboard, menu], local: true, html: { class: "space-y-4" }) do |form| %>
      <%= render "partials/dashboard/form_error", resource: menu %>

      <div class="form-control">
        <%= form.label translate_field(:name), class: "label" %>
        <%= form.text_field :name, class: "input w-full mt-2" %>
      </div>

      <div class="actions">
        <%= form.submit class: "btn btn-primary" %>
      </div>
    <% end %>
  </div>
</div>
