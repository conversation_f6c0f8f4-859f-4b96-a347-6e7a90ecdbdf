<div class="p-4">
  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <h1 class="text-2xl font-bold card-title mb-4"><%= @menu.name %></h1>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="card bg-base-200 shadow-inner">
          <div class="card-body">
            <h2 class="text-xl font-semibold mb-4 card-title">Menu Items</h2>
            <ul id="menu-items" class="menu bg-base-100 w-full rounded-box" data-reorder-url="<%= reorder_dashboard_menu_menu_items_path(@menu) %>">
              <% @menu.menu_items.each do |menu_item| %>
                <li data-id="<%= menu_item.id %>" class="flex flex-row items-center justify-between p-2 bg-base-100 rounded-box mb-2 shadow">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 cursor-grab" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                    <%= menu_item.name %>
                  </div>
                  <%= link_to 'Remove', dashboard_menu_menu_item_path(@menu, menu_item), method: :delete, data: { confirm: 'Are you sure?' }, class: "btn btn-xs btn-error" %>
                </li>
              <% end %>
            </ul>
          </div>
        </div>

        <div>
          <h2 class="text-xl font-semibold mb-4">Add Menu Item</h2>
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <%= form_with(model: [ :dashboard, @menu, @menu_item ], local: true, html: { class: "space-y-4" }) do |form| %>
                <div class="form-control">
                  <%= form.label :name, class: "label" %>
                  <%= form.text_field :name, class: "input input-bordered" %>
                </div>

                <div class="form-control">
                  <%= form.label :linkable_type, "Link to", class: "label" %>
                  <%= form.select :linkable_type, ['Category', 'Tag', 'Custom URL'], { include_blank: true }, { id: 'linkable-type', class: "select select-bordered" } %>
                </div>

                <div class="form-control" id="category-select" style="display: none;">
                  <%= form.label :linkable_id, "Category", class: "label" %>
                  <%= form.collection_select :linkable_id, Category.all, :id, :name, { include_blank: true }, { class: "select select-bordered" } %>
                </div>

                <div class="form-control" id="tag-select" style="display: none;">
                  <%= form.label :linkable_id, "Tag", class: "label" %>
                  <%= form.collection_select :linkable_id, Tag.all, :id, :name, { include_blank: true }, { class: "select select-bordered" } %>
                </div>

                <div class="form-control" id="custom-url-field" style="display: none;">
                  <%= form.label :url, class: "label" %>
                  <%= form.text_field :url, class: "input input-bordered" %>
                </div>

                <div class="actions">
                  <%= form.submit "Add Item", class: "btn btn-primary" %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <%= link_to 'Back', dashboard_menus_path, class: "btn" %>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
  document.addEventListener('turbo:load', function() {
    var el = document.getElementById('menu-items');
    if (el) {
      var sortable = Sortable.create(el, {
        animation: 150,
        handle: '.cursor-grab',
        onUpdate: function (evt) {
          var url = el.dataset.reorderUrl;
          var data = Array.from(el.children).map((child) => child.dataset.id);

          fetch(url, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': document.querySelector("[name='csrf-token']").content
            },
            body: JSON.stringify({ ids: data })
          });
        }
      });
    }

    var linkableType = document.getElementById('linkable-type');
    var categorySelect = document.getElementById('category-select');
    var tagSelect = document.getElementById('tag-select');
    var customUrlField = document.getElementById('custom-url-field');

    function toggleLinkableFields() {
      if (!linkableType) return;

      categorySelect.style.display = 'none';
      tagSelect.style.display = 'none';
      customUrlField.style.display = 'none';

      if (linkableType.value === 'Category') {
        categorySelect.style.display = 'block';
      } else if (linkableType.value === 'Tag') {
        tagSelect.style.display = 'block';
      } else if (linkableType.value === 'Custom URL') {
        customUrlField.style.display = 'block';
      }
    }

    if (linkableType) {
      linkableType.addEventListener('change', toggleLinkableFields);
    }
    
    toggleLinkableFields(); // Also run on page load
  });
</script>