<%= turbo_stream.remove @category %>
<%= turbo_stream.after "categories" do %>
  <script>
    (function(){
      // Create and append alerts
      var container = document.createElement('div');
      container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
      while (container.firstChild) {
        var alertElement = container.firstChild;
        document.body.appendChild(alertElement);

        // Manually trigger the alert animation since Stimulus might not connect automatically
        if (alertElement.hasAttribute('data-controller') && alertElement.getAttribute('data-controller').includes('alert')) {
          requestAnimationFrame(() => {
            alertElement.classList.remove("opacity-0", "translate-x-4");
          });
        }
      }

      // Remove this script element after execution
      var scripts = document.querySelectorAll('script');
      var thisScript = scripts[scripts.length - 1];
      if (thisScript) thisScript.remove();
    })();
  </script>
<% end %>