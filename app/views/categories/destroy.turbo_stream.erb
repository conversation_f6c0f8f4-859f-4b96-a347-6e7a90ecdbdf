<%= turbo_stream.remove @category %>
<%= turbo_stream.update "categories" do %>
  <script>
    console.log('Turbo stream executing!');
    console.log('Flash:', <%= flash.to_h.to_json.html_safe %>);

    // Create debug box
    var debugBox = document.createElement('div');
    debugBox.style.cssText = 'position: fixed; top: 10px; right: 10px; background: red; color: white; padding: 10px; z-index: 9999;';
    debugBox.innerHTML = 'DEBUG: Flash = <%= flash.to_h.inspect.html_safe %><br>Message: <%= flash[:notice] %>';
    document.body.appendChild(debugBox);

    // Create and append alerts
    var container = document.createElement('div');
    container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
    while (container.firstChild) {
      document.body.appendChild(container.firstChild);
    }

    setTimeout(() => debugBox.remove(), 5000);
  </script>
<% end %>