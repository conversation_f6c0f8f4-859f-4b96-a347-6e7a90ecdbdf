<%# 
  Reusable partial for destroy modal turbo stream responses
  
  Required locals:
  - resource: the destroyed resource instance (e.g., @tag, @category)
  - list_id: the ID of the tbody/container where the row existed (e.g., "tags", "categories")
%>

<%# Remove the destroyed resource from the list %>
<%= turbo_stream.remove resource %>

<%# Inject the rendered alerts partial into the page %>
<%= turbo_stream.append list_id do %>
    <script>
        (function(){
            console.log('Destroy turbo stream executing...');
            console.log('Flash messages:', <%= flash.to_h.to_json.html_safe %>);
            var container = document.createElement('div');
            container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
            console.log('Alert HTML:', container.innerHTML);
            while (container.firstChild) {
                console.log('Appending alert to body:', container.firstChild);
                document.body.appendChild(container.firstChild);
            }
            console.log('Destroy turbo stream completed');
        })();
    </script>
<% end %>
