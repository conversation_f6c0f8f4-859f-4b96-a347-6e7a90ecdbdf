<%# 
  Reusable partial for update modal turbo stream responses
  
  Required locals:
  - resource: the updated resource instance (e.g., @tag, @category)
  - list_id: the ID of the tbody/container where the row exists (e.g., "tags", "categories")
  - edit_path_helper: symbol for the edit path helper (e.g., :edit_dashboard_tag_path, :edit_dashboard_category_path)
  - show_path_helper: symbol for the show path helper (e.g., :tag_path, :category_path)
  - delete_path_helper: symbol for the delete path helper (e.g., :dashboard_tag_path, :dashboard_category_path)
  - additional_columns: array of additional column data to display (optional)
%>

<%# Replace the updated row in the list %>
<%= turbo_stream.replace dom_id(resource) do %>
  <tr id="<%= dom_id(resource) %>">
    <td><%= resource.name %></td>
    <% if additional_columns.present? %>
      <% additional_columns.each do |column| %>
        <td><%= resource.send(column) %></td>
      <% end %>
    <% end %>
    <td class="flex gap-2">
      <%= link_to translate_action(:show), send(show_path_helper, resource), class: "btn btn-dash btn-primary", target: "_blank" %>
      <button type="button" class="btn btn-dash btn-secondary" data-action="click->modal-remote#open" data-url="<%= send(edit_path_helper, resource, modal: 1) %>"><%= translate_action(:edit) %></button>
      <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= send(delete_path_helper, resource) %>" data-method="delete" data-name="<%= resource.name %>"><%= translate_action(:delete) %></button>
    </td>
  </tr>
<% end %>

<%# Close the remote modal and inject rendered alerts into the page %>
<%= turbo_stream.append list_id do %>
  <script>
    (function(){
      var chk = document.getElementById('remote_modal_toggle');
      if (chk) chk.checked = false;
      var container = document.createElement('div');
      container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
      while (container.firstChild) { document.body.appendChild(container.firstChild); }
      // Highlight the updated row briefly
      var row = document.getElementById('<%= dom_id(resource) %>');
      if (row) {
        row.classList.add('bg-success','transition-colors','duration-700');
        setTimeout(function(){ row.classList.remove('bg-success','transition-colors','duration-700'); }, 1500);
      }
    })();
  </script>
<% end %>
