# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user)
    # Define abilities for the user here. For example:
    #
    #   return unless user.present?
    #   can :read, :all
    #   return unless user.admin?
    #   can :manage, :all
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, published: true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/blob/develop/docs/define_check_abilities.md
    user ||= User.new # user not login

    # All logged-in users can update their own user settings
    can :update, :user_settings if user.persisted?
    can :read, :settings if user.persisted?

    if user.admin?
      can :manage, :all # Admins can do everything
      # Admins can destroy any user except themselves
      cannot :destroy, User, id: user.id
    else
      # Load permissions from the database for the user's role
      Permission.where(role: user.role).each do |permission|
        # e.g., can :read, Post
        # The subject_class is stored as a string. We need to check if it's a model ("Post")
        # or a symbol ("dashboard").
        subject = if (klass = permission.subject_class.safe_constantize)
          klass
        else
          permission.subject_class.to_sym
        end
        can permission.action.to_sym, subject
      end
      # Define abilities that depend on object attributes (e.g., user_id)
      # These are harder to model in a simple permissions table, so they can remain here.
      if user.author?
        # An author can update or destroy their OWN posts.
        # The database might grant :update, Post, but we restrict it here.
        can [ :update, :destroy ], Post, user_id: user.id
      end

      # Explicitly deny permissions
      if user.editor? || user.author? || user.viewer?
        cannot :update, :settings # Only admins can update site settings
      end

      cannot :read, :dashboard if user.viewer?
    end
  end
end
