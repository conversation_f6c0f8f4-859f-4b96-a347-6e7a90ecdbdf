class SettingsController < ApplicationController
  before_action :authorize_settings_update, only: [ :update ]
  before_action :authorize_settings, only: [ :index ]
  before_action :authenticate_user!
  before_action :set_settings_breadcrumbs
  layout "dashboard"
  include SettingsHelper

  def index
    set_meta_tags title: translate_page_title(:settings)

    # 1. Set the active tab from params, or default based on role.
    default_tab = current_user.admin? ? "site" : "user"
    @active_tab = params[:active_tab] || default_tab

    # 2. Validate the active tab to ensure it's either "site" or "user".
    #    Also, ensure non-admins cannot access the "site" tab.
    @active_tab = default_tab unless [ "site", "user" ].include?(@active_tab) && (current_user.admin? || @active_tab != "site")

    # Batch load all site settings
    site_settings = get_options({
      "site_name" => "Rorschools",
      "site_description" => "A school website",
      "site_light_theme" => "winter",
      "site_dark_theme" => "black",
      "site_default_locale" => "id",
      "site_default_time_zone" => "Asia/Jakarta"
    })

    @site_name = site_settings["site_name"]
    @site_description = site_settings["site_description"]
    @site_light_theme = site_settings["site_light_theme"]
    @site_dark_theme = site_settings["site_dark_theme"]

    @site_default_locale = site_settings["site_default_locale"]
    @site_default_time_zone = site_settings["site_default_time_zone"].sub(/^Asia\//, "")
    # Batch load user settings
    user_settings = get_user_options(current_user, {
      "light_theme" => "winter",
      "dark_theme" => "black",
      "time_zone" => "Asia/Jakarta"
    })

    @user_light_theme = user_settings["light_theme"]
    @user_dark_theme = user_settings["dark_theme"]
    @user_time_zone = user_settings["time_zone"].sub(/^Asia\//, "")

    @light_theme_list = [
      "light", "cupcake", "bumblebee", "emerald", "corporate", "retro", "cyberpunk", "valentine", "garden", "aqua", "lofi", "pastel", "fantasy", "wireframe", "cmyk", "autumn", "acid", "lemonade", "winter", "nord", "caramellatte", "silk"
    ]
    @dark_theme_list = [
      "dark", "synthwave", "halloween", "forest", "black", "luxury", "dracula", "business", "night", "coffee", "dim", "sunset", "abyss"
    ]
  end

  def update
    active_tab = setting_params.keys.any? { |k| k.start_with?("site_") } ? "site" : "user"

    setting_params.each do |key, value|
      if key.starts_with?("site_")
        update_option(key, value)
      else
        update_user_option(current_user, key, value)
      end
    end

    set_themes

    respond_to do |format|
      format.html { redirect_to dashboard_settings_path(active_tab: active_tab), notice: translate_flash_notice(:updated, :settings) }
      format.json { head :ok }
    end
  end

  private

  def authorize_settings_update
    if setting_params.keys.any? { |k| k.start_with?("site_") }
      authorize! :update, :settings
    else
      authorize! :update, :user_settings
    end
  end

  def authorize_settings
    authorize! :read, :settings
  end

  def setting_params
    params.require(:setting).permit(:site_name, :site_description, :site_light_theme, :site_dark_theme, :site_default_locale, :site_default_time_zone, :light_theme, :dark_theme, :time_zone)
  end

  def set_settings_breadcrumbs
    helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path
    helpers.add_breadcrumb translate_page_title(:settings), dashboard_settings_path
  end
end
