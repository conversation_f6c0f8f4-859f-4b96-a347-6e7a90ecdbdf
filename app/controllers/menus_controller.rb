class MenusController < ApplicationController
  before_action :authorize_menus
   before_action :authenticate_user!
  before_action :set_menu_with_items, only: [ :show ]
  before_action :set_menu, only: [ :edit, :update, :destroy ]
  layout "dashboard"
  def index
    @menus = Menu.all
  end

  def show
    @menu_item = MenuItem.new
  end

  def new
    @menu = Menu.new
  end

  def edit
  end

  def create
    @menu = Menu.new(menu_params)
    if @menu.save
      redirect_to [ :dashboard, @menu ], notice: "Menu was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @menu.update(menu_params)
      redirect_to dashboard_menu_path(@menu), notice: "<PERSON><PERSON> was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @menu.destroy!
    redirect_to dashboard_menus_url, notice: "<PERSON><PERSON> was successfully destroyed.", status: :see_other
  end

  private

  def authorize_menus
    authorize! :read, Menu
  end

  def set_menu
    @menu = Menu.find(params[:id])
  end

  def set_menu_with_items
    @menu = Menu.includes(:menu_items).find(params[:id])
  end

  def menu_params
    params.require(:menu).permit(:name)
  end
end
