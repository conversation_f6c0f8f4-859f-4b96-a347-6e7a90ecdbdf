class MenuItemsController < ApplicationController
  before_action :set_menu_item, only: [ :update, :destroy ]

  def create
    @menu = Menu.find(params[:menu_id])
    @menu_item = @menu.menu_items.build(menu_item_params)
    if @menu_item.save
      redirect_to [ :dashboard, @menu ], notice: "Menu item was successfully created."
    else
      # The @menu instance variable is already set and has the invalid @menu_item.
      # We just need to ensure its menu_items are loaded for the re-render.
      @menu.menu_items.reload
      render "menus/show", status: :unprocessable_entity
    end
  end

  def update
    if @menu_item.update(menu_item_params)
      redirect_to [ :dashboard, @menu_item.menu ], notice: "Menu item was successfully updated."
    else
      @menu = @menu_item.menu
      # You might need to eager load here as well if the update fails and you re-render
      render "menus/show", status: :unprocessable_entity
    end
  end

  def destroy
    @menu_item.destroy!
    redirect_to [ :dashboard, @menu_item.menu ], notice: "Menu item was successfully destroyed.", status: :see_other
  end

  def reorder
    params[:ids].each_with_index do |id, index|
      MenuItem.find(id).update!(position: index + 1)
    end
    head :ok
  end

  private

  def set_menu_item
    @menu_item = MenuItem.find(params[:id])
  end

  def menu_item_params
    params.require(:menu_item).permit(:name, :linkable_id, :linkable_type, :url)
  end
end
